﻿// Add smooth scrolling for navigation links
document.querySelectorAll('.nav-item').forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();

        // Remove active class from all links
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to clicked link
        link.classList.add('active');
    });
});

// Handle CTA button click
document.querySelector('.cta-button').addEventListener('click', () => {
    // Add your call-to-action logic here
    console.log('CTA button clicked');
});

// Handle subject card clicks
document.querySelectorAll('.subject-card').forEach(card => {
    card.addEventListener('click', () => {
        // Add your subject navigation logic here
        console.log('Subject clicked:', card.querySelector('h4').textContent);
    });
});

// Handle course button clicks
document.querySelectorAll('.course-button').forEach(button => {
    button.addEventListener('click', () => {
        // Add your course navigation logic here
        const courseTitle = button.closest('.course-card').querySelector('h3').textContent;
        console.log('Course selected:', courseTitle);
    });
});

// Handle article card clicks
document.querySelectorAll('.article-card').forEach(card => {
    card.addEventListener('click', () => {
        // Add your article navigation logic here
        console.log('Article clicked:', card.querySelector('h3').textContent);
    });
});

// Handle social widget clicks
document.querySelectorAll('.social-widget img').forEach((icon, index) => {
    icon.addEventListener('click', () => {
        // Add your social media link logic here
        console.log('Social icon clicked:', index + 1);
    });
});